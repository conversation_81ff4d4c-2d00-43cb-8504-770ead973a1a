package com.xhcai.modules.rag.service.processor.impl;

import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;
import com.xhcai.modules.rag.service.processor.AbstractFileSegmentationProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTFldChar;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STFldCharType;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Word文档分段处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class WordFileSegmentationProcessor extends AbstractFileSegmentationProcessor {

    @Override
    public List<String> getSupportedFileTypes() {
        return Arrays.asList("doc", "docx");
    }

    @Override
    public String getProcessorName() {
        return "Word文档分段处理器";
    }

    @Override
    public int getPriority() {
        return 30;
    }

    @Override
    public List<SegmentResult> processSegmentation(Document document, InputStream inputStream, KnowledgeSegmentConfig knowledgeSegmentConfig) throws Exception {
        log.info("开始处理Word文档分段: documentId={}, fileName={}", document.getId(), document.getName());

        try {
            String text = extractTextFromWord(document, inputStream);
            text = cleanText(text, knowledgeSegmentConfig.getCleaningConfig());
            
            log.debug("Word文档内容长度: {} 字符", text.length());

            List<SegmentResult> segments = null;
            String type = knowledgeSegmentConfig.getSegmentConfig().getType();
            switch (type) {
                case "directory":
                    segments = processSegmentationCatalog(document, inputStream, knowledgeSegmentConfig);
                    break;
                case "constantLength":
                    segments = segmentByFixedSize(text, knowledgeSegmentConfig.getSegmentConfig().getConstantLength());
                    break;
                case "natural":
                    segments = segmentByParagraphs(text, knowledgeSegmentConfig.getSegmentConfig().getNatural());
                    break;
                case "delimiter":
                    segments = segmentByDelimiter(text, knowledgeSegmentConfig.getSegmentConfig().getDelimiter());
                    break;
                case "none":
                    segments = List.of(SegmentResult.create(text, 1, extractKeywords(text)));
                    break;
            }

            log.info("Word文档分段完成: documentId={}, 分段数量={}", document.getId(), segments.size());
            return segments;
        } catch (Exception e) {
            log.error("Word文档分段处理失败: documentId={}, error={}", document.getId(), e.getMessage(), e);
            throw new Exception("Word文档分段处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按目录读取文件并进行分段
     * @param document
     * @param inputStream
     * @return
     */
    public List<SegmentResult> processSegmentationCatalog(Document document, InputStream inputStream, KnowledgeSegmentConfig knowledgeSegmentConfig){
        log.info("开始按目录结构处理Word文档分段: documentId={}, fileName={}",
                document != null ? document.getId() : "test",
                document != null ? document.getName() : "test.doc");

        try {
            // 1. 判断是否存在目录结构
            List<CatalogItem> catalogItems = extractCatalogStructure(document, inputStream);

            if (catalogItems.isEmpty()) {
                log.info("文档不存在目录结构，无需处理");
                return new ArrayList<>();
            }

            log.info("检测到目录结构，共{}个目录项", catalogItems.size());

            // 2. 按目录分段读取文件内容
            List<SegmentResult> segments = segmentByCatalog(catalogItems, knowledgeSegmentConfig);

            log.info("按目录分段完成，共生成{}个分段", segments.size());
            return segments;

        } catch (Exception e) {
            log.error("按目录处理Word文档分段失败: error={}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 提取Word文档的目录结构
     * @param document 文档信息
     * @param inputStream 输入流
     * @return 目录项列表
     * @throws Exception
     */
    private List<CatalogItem> extractCatalogStructure(Document document, InputStream inputStream) throws Exception {
        List<CatalogItem> catalogItems = new ArrayList<>();

        String fileExtension = document != null ? document.getDocType() : "docx";

        // 首先尝试根据文件扩展名处理
        try {
            if ("docx".equals(fileExtension)) {
                catalogItems = extractCatalogFromDocx(inputStream);
            } else if ("doc".equals(fileExtension)) {
                catalogItems = extractCatalogFromDoc(inputStream);
            }
        } catch (Exception e) {
            log.warn("按文件扩展名{}处理失败: {}, 尝试自动检测格式", fileExtension, e.getMessage());

            // 如果按扩展名处理失败，尝试另一种格式
            try {
                if ("doc".equals(fileExtension)) {
                    log.info("尝试按docx格式重新处理");
                    catalogItems = extractCatalogFromDocx(inputStream);
                } else {
                    log.info("尝试按doc格式重新处理");
                    catalogItems = extractCatalogFromDoc(inputStream);
                }
            } catch (Exception e2) {
                log.error("两种格式都处理失败: docx错误={}, doc错误={}",
                    "docx".equals(fileExtension) ? e.getMessage() : e2.getMessage(),
                    "doc".equals(fileExtension) ? e.getMessage() : e2.getMessage());
                throw new Exception("无法识别的Word文档格式", e);
            }
        }

        return catalogItems;
    }

    /**
     * 从DOCX文件提取目录结构
     * @param inputStream 输入流
     * @return 目录项列表
     * @throws Exception
     */
    private List<CatalogItem> extractCatalogFromDocx(InputStream inputStream) throws Exception {
        List<CatalogItem> catalogItems = new ArrayList<>();
        StringBuilder orphanContent = new StringBuilder(); // 存储没有标题的内容

        try (XWPFDocument xwpfDocument = new XWPFDocument(inputStream)) {
            List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();

            for (int i = 0; i < paragraphs.size(); i++) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String text = paragraph.getText().trim();

                if (text.isEmpty()) {
                    continue;
                }

                // 检查是否为标题段落
                int headingLevel = getHeadingLevel(paragraph, xwpfDocument);
                if (headingLevel > 0) {
                    // 如果有孤立内容，先创建一个默认目录项
                    if (orphanContent.length() > 0 && catalogItems.isEmpty()) {
                        CatalogItem defaultItem = new CatalogItem();
                        defaultItem.setTitle("文档内容");
                        defaultItem.setLevel(1);
                        defaultItem.setStartIndex(0);
                        defaultItem.setContent(orphanContent);
                        catalogItems.add(defaultItem);
                        log.debug("创建默认目录项包含孤立内容: {} 字符", orphanContent.length());
                        orphanContent = new StringBuilder(); // 重置
                    }

                    CatalogItem item = new CatalogItem();
                    item.setTitle(text);
                    item.setLevel(headingLevel);
                    item.setStartIndex(i);
                    item.setContent(new StringBuilder());
                    catalogItems.add(item);

                    log.debug("发现{}级标题: {}", headingLevel, text);
                } else {
                    if (!catalogItems.isEmpty()) {
                        // 将非标题内容添加到最后一个目录项中
                        CatalogItem lastItem = catalogItems.getLast();
                        lastItem.getContent().append(text).append("\n");
                    } else {
                        // 如果还没有目录项，暂存到孤立内容中
                        orphanContent.append(text).append("\n");
                    }
                }
            }

            // 处理剩余的孤立内容
            if (orphanContent.length() > 0) {
                if (catalogItems.isEmpty()) {
                    // 如果没有任何标题，创建一个默认目录项包含所有内容
                    CatalogItem defaultItem = new CatalogItem();
                    defaultItem.setTitle("文档内容");
                    defaultItem.setLevel(1);
                    defaultItem.setStartIndex(0);
                    defaultItem.setContent(orphanContent);
                    catalogItems.add(defaultItem);
                    log.debug("创建默认目录项包含所有内容: {} 字符", orphanContent.length());
                } else {
                    // 如果有标题，将剩余内容添加到最后一个目录项
                    CatalogItem lastItem = catalogItems.getLast();
                    lastItem.getContent().append(orphanContent);
                    log.debug("将剩余内容添加到最后一个目录项: {} 字符", orphanContent.length());
                }
            }
        }

        return catalogItems;
    }

    /**
     * 从DOC文件提取目录结构
     * @param inputStream 输入流
     * @return 目录项列表
     * @throws Exception
     */
    private List<CatalogItem> extractCatalogFromDoc(InputStream inputStream) throws Exception {
        List<CatalogItem> catalogItems = new ArrayList<>();
        StringBuilder orphanContent = new StringBuilder(); // 存储没有标题的内容

        try (HWPFDocument hwpfDocument = new HWPFDocument(inputStream);
             WordExtractor extractor = new WordExtractor(hwpfDocument)) {

            String[] paragraphs = extractor.getParagraphText();

            for (int i = 0; i < paragraphs.length; i++) {
                String text = paragraphs[i].trim();

                if (text.isEmpty()) {
                    continue;
                }

                // 对于DOC文件，使用简单的标题识别规则
                int headingLevel = detectHeadingLevelFromText(text);
                if (headingLevel > 0) {
                    // 如果有孤立内容，先创建一个默认目录项
                    if (orphanContent.length() > 0 && catalogItems.isEmpty()) {
                        CatalogItem defaultItem = new CatalogItem();
                        defaultItem.setTitle("文档内容");
                        defaultItem.setLevel(1);
                        defaultItem.setStartIndex(0);
                        defaultItem.setContent(orphanContent);
                        catalogItems.add(defaultItem);
                        log.debug("创建默认目录项包含孤立内容: {} 字符", orphanContent.length());
                        orphanContent = new StringBuilder(); // 重置
                    }

                    CatalogItem item = new CatalogItem();
                    item.setTitle(text);
                    item.setLevel(headingLevel);
                    item.setStartIndex(i);
                    item.setContent(new StringBuilder());
                    catalogItems.add(item);

                    log.debug("发现{}级标题: {}", headingLevel, text);
                } else {
                    if (!catalogItems.isEmpty()) {
                        // 将非标题内容添加到最后一个目录项中
                        CatalogItem lastItem = catalogItems.get(catalogItems.size() - 1);
                        lastItem.getContent().append(text).append("\n");
                    } else {
                        // 如果还没有目录项，暂存到孤立内容中
                        orphanContent.append(text).append("\n");
                    }
                }
            }

            // 处理剩余的孤立内容
            if (orphanContent.length() > 0) {
                if (catalogItems.isEmpty()) {
                    // 如果没有任何标题，创建一个默认目录项包含所有内容
                    CatalogItem defaultItem = new CatalogItem();
                    defaultItem.setTitle("文档内容");
                    defaultItem.setLevel(1);
                    defaultItem.setStartIndex(0);
                    defaultItem.setContent(orphanContent);
                    catalogItems.add(defaultItem);
                    log.debug("创建默认目录项包含所有内容: {} 字符", orphanContent.length());
                } else {
                    // 如果有标题，将剩余内容添加到最后一个目录项
                    CatalogItem lastItem = catalogItems.get(catalogItems.size() - 1);
                    lastItem.getContent().append(orphanContent);
                    log.debug("将剩余内容添加到最后一个目录项: {} 字符", orphanContent.length());
                }
            }
        }

        return catalogItems;
    }

    /**
     * 获取段落的标题级别（增强版本，包含目录段落检查）
     * @param paragraph XWPF段落
     * @param document XWPF文档
     * @return 标题级别，0表示非标题
     */
    private int getHeadingLevel(XWPFParagraph paragraph, XWPFDocument document) {
        try {
            String text = paragraph.getText().trim();
            String styleId = paragraph.getStyleID();

            log.debug("检查段落: text='{}', styleId='{}'",
                text.length() > 50 ? text.substring(0, 50) + "..." : text, styleId);

            // ✅ 1. 检查段落样式是否为标题样式
            if (styleId != null) {
                String lowerStyleId = styleId.toLowerCase();
                log.debug("段落样式ID: {}", lowerStyleId);

                if (lowerStyleId.startsWith("heading")) {
                    String levelStr = lowerStyleId.replace("heading", "").trim();
                    try {
                        int level = Integer.parseInt(levelStr);
                        log.debug("从样式ID识别标题级别: {}", level);
                        return level;
                    } catch (NumberFormatException e) {
                        log.debug("样式ID中的级别解析失败: {}", levelStr);
                    }
                }

                // 检查目录相关样式
                if (lowerStyleId.contains("toc") || lowerStyleId.contains("目录")) {
                    log.debug("发现目录样式: {}", styleId);
                    // 尝试从目录样式中提取级别
                    for (int i = 1; i <= 6; i++) {
                        if (lowerStyleId.contains(String.valueOf(i))) {
                            log.debug("从目录样式识别标题级别: {}", i);
                            return i;
                        }
                    }
                    return 1; // 默认目录为1级标题
                }
            }

            // ✅ 2. 检查样式名称
            if (document.getStyles() != null && styleId != null && document.getStyles().getStyle(styleId) != null) {
                String styleName = document.getStyles().getStyle(styleId).getName();
                if (styleName != null) {
                    String lowerStyleName = styleName.toLowerCase();
                    log.debug("段落样式名称: {}", styleName);

                    if (lowerStyleName.contains("heading") || lowerStyleName.contains("标题") ||
                        lowerStyleName.contains("toc") || lowerStyleName.contains("目录")) {
                        // 尝试从样式名中提取级别
                        for (int i = 1; i <= 6; i++) {
                            if (lowerStyleName.contains(String.valueOf(i))) {
                                log.debug("从样式名称识别标题级别: {}", i);
                                return i;
                            }
                        }
                        log.debug("从样式名称识别为默认1级标题");
                        return 1; // 默认为1级标题
                    }
                }
            }

            // ✅ 3. 检查大纲级别
            try {
                if (paragraph.getCTP().getPPr() != null &&
                    paragraph.getCTP().getPPr().getOutlineLvl() != null) {
                    int outlineLevel = paragraph.getCTP().getPPr().getOutlineLvl().getVal().intValue() + 1;
                    log.debug("从大纲级别识别标题级别: {}", outlineLevel);
                    return outlineLevel;
                }
            } catch (Exception e) {
                log.debug("检查大纲级别时出错: {}", e.getMessage());
            }

            // ✅ 4. 检查是否为目录段落（TOC段落）
            try {
                if (isTOCParagraph(paragraph)) {
                    log.debug("识别为目录段落: {}", text.length() > 50 ? text.substring(0, 50) + "..." : text);
                    // 目录段落通常作为2级标题处理，但可以根据需要调整
                    return 2;
                }
            } catch (Exception e) {
                log.debug("TOC段落检查出错，跳过: {}", e.getMessage());
                // 如果TOC检查出错，继续其他检查，不影响整体功能
            }


        } catch (Exception e) {
            log.debug("检查段落标题级别时出错: {}", e.getMessage());
        }

        return 0;
    }

    /**
     * 判断一个段落是否是目录段落（修正POI API兼容性问题）
     * @param paragraph XWPFParagraph对象
     * @return 如果是目录则返回true
     */
    private boolean isTOCParagraph(XWPFParagraph paragraph) {
        try {
            // 1. 获取段落的底层XML对象
            CTP ctp = paragraph.getCTP();
            if (ctp == null) {
                return false;
            }

            // 2. 遍历段落中的所有"运行"(r)元素
            for (CTR ctr : ctp.getRList()) {
                if (ctr == null) {
                    continue;
                }

                // 3. 检查是否存在字段字符 (fldChar)
                if (ctr.sizeOfFldCharArray() > 0) {
                    for (int i = 0; i < ctr.sizeOfFldCharArray(); i++) {
                        CTFldChar fldChar = ctr.getFldCharArray(i);
                        if (fldChar != null && fldChar.getFldCharType() == STFldCharType.BEGIN) {
                            log.debug("发现字段开始标记");
                            // 检查后续的指令文本
                            if (checkTOCInstructionInRuns(ctp.getRList(), ctr)) {
                                return true;
                            }
                        }
                    }
                }

                // 4. 检查指令文本 (instrText)
                if (ctr.sizeOfInstrTextArray() > 0) {
                    for (int i = 0; i < ctr.sizeOfInstrTextArray(); i++) {
                        String instrText = ctr.getInstrTextArray(i).getStringValue();
                        if (instrText != null && instrText.contains("TOC")) {
                            log.debug("通过指令文本识别为TOC段落: {}", instrText);
                            return true;
                        }
                    }
                }

//                // 5. 检查普通文本运行
//                if (ctr.sizeOfTArray() > 0) {
//                    for (int i = 0; i < ctr.sizeOfTArray(); i++) {
//                        String text = ctr.getTArray(i).getStringValue();
//                        if (text != null && text.contains("TOC")) {
//                            log.debug("通过文本内容识别为TOC段落: {}", text);
//                            return true;
//                        }
//                    }
//                }
            }

//            // 6. 额外检查：通过段落文本内容判断
//            String paragraphText = paragraph.getText();
//            if (paragraphText != null && paragraphText.trim().length() > 0) {
//                // 检查是否包含典型的目录模式
//                if (isTOCLikeContent(paragraphText)) {
//                    log.debug("通过内容模式识别为可能的TOC段落: {}",
//                        paragraphText.length() > 100 ? paragraphText.substring(0, 100) + "..." : paragraphText);
//                    return true;
//                }
//            }

        } catch (Exception e) {
            log.debug("检查TOC段落时出错: {}", e.getMessage());
        }

        return false;
    }

    /**
     * 在运行列表中检查TOC指令
     * @param runs 运行列表
     * @param currentRun 当前运行
     * @return 是否找到TOC指令
     */
    private boolean checkTOCInstructionInRuns(java.util.List<CTR> runs, CTR currentRun) {
        try {
            int currentIndex = runs.indexOf(currentRun);
            // 检查当前运行之后的几个运行，寻找TOC指令
            for (int i = currentIndex + 1; i < Math.min(currentIndex + 5, runs.size()); i++) {
                CTR nextRun = runs.get(i);
                if (nextRun == null) {
                    continue;
                }

                // 检查指令文本
                if (nextRun.sizeOfInstrTextArray() > 0) {
                    for (int j = 0; j < nextRun.sizeOfInstrTextArray(); j++) {
                        String instrText = nextRun.getInstrTextArray(j).getStringValue();
                        if (instrText != null && instrText.contains("TOC")) {
                            log.debug("在相邻运行中找到TOC指令: {}", instrText);
                            return true;
                        }
                    }
                }

                // 检查普通文本
                if (nextRun.sizeOfTArray() > 0) {
                    for (int j = 0; j < nextRun.sizeOfTArray(); j++) {
                        String text = nextRun.getTArray(j).getStringValue();
                        if (text != null && text.contains("TOC")) {
                            log.debug("在相邻运行的文本中找到TOC: {}", text);
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("检查相邻运行中的TOC指令时出错: {}", e.getMessage());
        }

        return false;
    }

    /**
     * 从Word文档提取文本
     *
     * @param document    文档信息
     * @param inputStream 输入流
     * @return 提取的文本
     * @throws Exception 提取异常
     */
    private String extractTextFromWord(Document document, InputStream inputStream) throws Exception {
        String fileExtension = document.getDocType();
        
        if ("docx".equals(fileExtension)) {
            // 处理.docx文件
            try (XWPFDocument xwpfDocument = new XWPFDocument(inputStream);
                 XWPFWordExtractor extractor = new XWPFWordExtractor(xwpfDocument)) {
                return extractor.getText();
            }
        } else if ("doc".equals(fileExtension)) {
            // 处理.doc文件
            try (HWPFDocument hwpfDocument = new HWPFDocument(inputStream);
                 WordExtractor extractor = new WordExtractor(hwpfDocument)) {
                return extractor.getText();
            }
        } else {
            throw new Exception("不支持的Word文档格式: " + fileExtension);
        }
    }
}
