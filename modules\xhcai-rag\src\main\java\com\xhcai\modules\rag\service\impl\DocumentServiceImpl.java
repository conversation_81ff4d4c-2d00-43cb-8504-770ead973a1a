package com.xhcai.modules.rag.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import com.xhcai.modules.rag.entity.inner.FileCleanSegmentConfig;
import org.apache.tika.Tika;
import org.apache.tika.exception.TikaException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.config.RagProperties;
import com.xhcai.modules.rag.dto.BatchSegmentationRequestDTO;
import com.xhcai.modules.rag.dto.FileCleanSegmentConfigParamsDTO;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.enums.DataSourceType;
import com.xhcai.modules.rag.enums.DocumentStatus;
import com.xhcai.modules.rag.mapper.DocumentMapper;
import com.xhcai.modules.rag.plugins.minio.MinioStorageService;
import com.xhcai.modules.rag.plugins.rabbitmq.producer.RabbitMQProducer;
import com.xhcai.modules.rag.service.IDocumentProcessingService;
import com.xhcai.modules.rag.service.IDocumentService;
import com.xhcai.modules.rag.vo.DocumentVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;

/**
 * 文档服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class DocumentServiceImpl extends ServiceImpl<DocumentMapper, Document> implements IDocumentService {

    private static final Logger log = LoggerFactory.getLogger(DocumentServiceImpl.class);

    @Autowired
    private RagProperties ragProperties;

    @Autowired
    private IDocumentProcessingService documentProcessingService;

    @Autowired
    private DocumentMapper documentMapper;

    @Autowired
    private MinioStorageService minioStorageService;

    @Autowired(required = false)
    private RabbitMQProducer rabbitMQProducer;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    private final Tika tika = new Tika();

    // 支持的文件类型
    private static final Set<String> SUPPORTED_FILE_TYPES = Set.of(
            "pdf", "doc", "docx", "txt", "md", "html", "csv", "xlsx", "pptx", "rtf"
    );

    @Override
    public IPage<DocumentVO> pageByDataset(Long current, Long size, String datasetId, String name,
            String documentStatus, Boolean enabled, String batch) {
        Page<Document> page = new Page<>(current, size);
        return baseMapper.selectPageByDataset(page, datasetId, name, documentStatus, enabled, batch);
    }

    @Override
    public List<DocumentVO> listByDatasetId(String datasetId) {
        return baseMapper.selectByDatasetId(datasetId);
    }

    @Override
    public List<DocumentVO> batchUploadDocuments(List<MultipartFile> files, String datasetId, String userId, String batchId) {
        log.info("批量上传文档: fileCount={}, datasetId={}, userId={}", files.size(), datasetId, userId);

        List<DocumentVO> documents = new ArrayList<>();

        // 获取当前用户信息
        String currentUserId = SecurityUtils.getCurrentUserId();
        String currentTenantId = SecurityUtils.getCurrentTenantId();

//        // 验证权限
//        if (!SecurityUtils.hasPermission("rag:document:upload")) {
//            throw new BusinessException("没有文档上传权限");
//        }
        // 检查批量上传限制
        if (files.size() > ragProperties.getLimits().getMaxUploadFiles()) {
            throw new BusinessException("单次上传文件数量超过限制: " + ragProperties.getLimits().getMaxUploadFiles());
        }

        // 检查文档数量限制
        checkDocumentLimit(datasetId);
        checkTenantDocumentLimit(currentTenantId);

        int maxPosition = documentMapper.selectMaxPosition(datasetId);
        AtomicInteger position = new AtomicInteger(maxPosition);

        for (MultipartFile file : files) {
            position.getAndIncrement();
            String originalFilename = file.getOriginalFilename();
            Document document = new Document();
            // 生成对象名称  上传到目录格式， 知识库ID/文档ID(目录)/文档ID
            String objectName = null;

            try {
                // 验证文件
                validateFile(file);
                Wrapper<Document> wrapper = new QueryWrapper<Document>()
                        .eq("dataset_id", datasetId)
                        .eq("name", originalFilename);
                Document documentSelected = documentMapper.selectOne(wrapper);

                if (ObjUtil.isNotNull(documentSelected)) {
                    // 已存在的文档
                    document = documentSelected;
                    document.setUpdateTime(LocalDateTime.now());
                    document.setUpdateBy(currentUserId);
                    Map<String, Object> docMetadata = document.getDocMetadata();
                    objectName = MapUtil.getStr(docMetadata, "save_name", datasetId + "/" + document.getId() + "/" + document.getId() + "." + document.getDocType());
                    document.setDocumentStatus(documentSelected.getDocumentStatus());
                } else {
                    // 创建文档记录
                    String documentId = UUID.randomUUID().toString();
                    document = createDocumentFromFile(file, datasetId, currentUserId, position.get());
                    document.setId(documentId);
                    objectName = datasetId + "/" + documentId + "/" + documentId + "." + document.getDocType();
                }

                document.setBatch(batchId);

                // 上传文件到文件服务器
                try (InputStream inputStream = file.getInputStream()) {
                    // 检查存储服务类型并上传文件
                    String url = minioStorageService.uploadFile(objectName, inputStream, file.getContentType());

                    Map<String, Object> docMetadata = document.getDocMetadata();
                    docMetadata.put("preview_url", url);
                    docMetadata.put("save_name", objectName);
                    docMetadata.put("save_type", "minio");
                    document.setDocumentStatus(DocumentStatus.UPLOADED.getCode());
                    document.setError("");
                } catch (Exception e) {
                    log.error("Failed to upload file to storage service: {}", e.getMessage(), e);
                    // 不抛出异常，允许文档记录保存，但记录错误信息
                    document.setDocumentStatus(DocumentStatus.UPLOAD_ERROR.getCode());
                    document.setError(e.getMessage());
                }

                // 保存文档记录
                if (saveOrUpdate(document)) {
                    DocumentVO documentVO = new DocumentVO();
                    BeanUtils.copyProperties(document, documentVO);
                    documentVO.setDocumentStatusDesc(DocumentStatus.fromCode(document.getDocumentStatus()).getDescription());
                    documentVO.setDocumentStatusBgColor(DocumentStatus.fromCode(document.getDocumentStatus()).getCss());
                    documents.add(documentVO);
                } else {
                    log.error("批量上传文档更新Db时失败: fileName={}", file.getOriginalFilename());
                    DocumentVO documentVO = new DocumentVO();
                    BeanUtils.copyProperties(document, documentVO);
                    documentVO.setDocumentStatus(DocumentStatus.SAVE_ERROR.getCode());
                    documentVO.setDocumentStatusDesc(DocumentStatus.SAVE_ERROR.getDescription());
                    documentVO.setDocumentStatusBgColor(DocumentStatus.SAVE_ERROR.getCss());
                    documents.add(documentVO);
                }
            } catch (Exception e) {
                log.error("批量上传文档异常: fileName={}, error={}", file.getOriginalFilename(), e.getMessage());
                document.setError("整个过程出现异常，%s".formatted(e.getMessage()));

                DocumentVO documentVO = new DocumentVO();
                BeanUtils.copyProperties(document, documentVO);
                documentVO.setDocumentStatus(DocumentStatus.SAVE_ERROR.getCode());
                documentVO.setDocumentStatusDesc(DocumentStatus.SAVE_ERROR.getDescription());
                documentVO.setDocumentStatusBgColor(DocumentStatus.SAVE_ERROR.getCss());
                documents.add(documentVO);

                try {
                    saveOrUpdate(document);
                } catch (Exception e1) {
                    log.error("批量上传文档更新Db时失败: fileName={}, error={}", file.getOriginalFilename(), e1.getMessage());
                }
            }
        }
        return documents;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Document updateDocument(Document document) {
        log.info("更新文档: documentId={}", document.getId());

        // 验证权限
        if (!SecurityUtils.hasPermission("rag:document:update")) {
            throw new BusinessException("没有文档更新权限");
        }

        // 检查文档是否存在
        Document existingDocument = getById(document.getId());
        if (existingDocument == null) {
            throw new BusinessException("文档不存在");
        }

        // 检查数据权限 - 只能编辑自己创建的文档或有管理权限
        String currentUserId = SecurityUtils.getCurrentUserId();
        if (!existingDocument.getCreateBy().equals(currentUserId)
                && !SecurityUtils.hasPermission("rag:document:manage")) {
            throw new BusinessException("没有权限编辑此文档");
        }

        // 检查文档是否可以编辑
        if (!canEdit(document.getId())) {
            throw new BusinessException("文档当前状态不允许编辑");
        }

        document.setUpdateTime(LocalDateTime.now());

        if (updateById(document)) {
            log.info("文档更新成功: documentId={}", document.getId());
            return getById(document.getId());
        } else {
            throw new BusinessException("文档更新失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDocument(String id) {
        log.info("删除文档: documentId={}", id);

        // 验证权限
        if (!SecurityUtils.hasPermission("rag:document:delete")) {
            throw new BusinessException("没有文档删除权限");
        }

        // 检查文档是否存在
        Document document = getById(id);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        // 检查数据权限 - 只能删除自己创建的文档或有管理权限
        String currentUserId = SecurityUtils.getCurrentUserId();
        if (!document.getCreateBy().equals(currentUserId)
                && !SecurityUtils.hasPermission("rag:document:manage")) {
            throw new BusinessException("没有权限删除此文档");
        }

        // 检查文档是否可以删除
        if (!canDelete(id)) {
            throw new BusinessException("文档当前状态不允许删除");
        }

        // TODO: 删除相关的文档分段和向量数据
        // deleteDocumentSegments(id);
        // deleteDocumentVectors(id);
        if (removeById(id)) {
            log.info("文档删除成功: documentId={}", id);
            return true;
        } else {
            throw new BusinessException("文档删除失败");
        }
    }

    @Override
    public Document getDocumentById(String id) {
        return getById(id);
    }

    @Override
    public Long countByDatasetId(String datasetId) {
        return baseMapper.countByDatasetId(datasetId);
    }

    @Override
    public Long countByDatasetIdAndStatus(String datasetId, String documentStatus) {
        return baseMapper.countByDatasetIdAndStatus(datasetId, documentStatus);
    }

    /**
     * 验证上传的文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            throw new BusinessException("文件名不能为空");
        }

        // 检查文件大小
        if (file.getSize() > ragProperties.getLimits().getMaxDocumentSize()) {
            throw new BusinessException("文件大小超过限制: " + ragProperties.getLimits().getMaxDocumentSize() + " 字节");
        }

        // 检查文件类型
        String fileExtension = getFileExtension(originalFilename);
        if (!SUPPORTED_FILE_TYPES.contains(fileExtension.toLowerCase())) {
            throw new BusinessException("不支持的文件类型: " + fileExtension);
        }

        // 使用Tika检测文件内容类型，验证文件真实性
        try (InputStream inputStream = file.getInputStream()) {
            String detectedType = tika.detect(inputStream, originalFilename);
            log.debug("检测到文件类型: {}", detectedType);

            // 验证文件内容与扩展名是否匹配
            if (!isValidFileType(detectedType, fileExtension)) {
                throw new BusinessException("文件内容与扩展名不匹配，可能是恶意文件");
            }
        } catch (IOException e) {
            log.error("文件内容验证失败: {}", e.getMessage());
            throw new BusinessException("文件内容验证失败: " + e.getMessage());
        }
    }

    /**
     * 验证文件内容类型与扩展名是否匹配
     */
    private boolean isValidFileType(String detectedType, String fileExtension) {
        if (detectedType == null) {
            return false;
        }

        String lowerExtension = fileExtension.toLowerCase();
        String lowerDetectedType = detectedType.toLowerCase();

        // 定义文件类型映射
        Map<String, List<String>> typeMapping = Map.of(
                "pdf", List.of("application/pdf"),
                "doc", List.of("application/msword"),
                "docx", List.of("application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
                "txt", List.of("text/plain", "text/x-c", "text/x-java"),
                "md", List.of("text/plain", "text/x-web-markdown"),
                "rtf", List.of("application/rtf", "text/rtf")
        );

        List<String> validTypes = typeMapping.get(lowerExtension);
        if (validTypes == null) {
            return false;
        }

        return validTypes.stream().anyMatch(lowerDetectedType::contains);
    }

    /**
     * 从上传文件创建文档记录
     */
    private Document createDocumentFromFile(MultipartFile file, String datasetId, String userId, int position) {
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);

        Document document = new Document();
        document.setDatasetId(datasetId);
        document.setName(originalFilename);
        document.setDataSourceType(DataSourceType.UPLOAD_FILE.getCode());
//        document.setDataSourceInfo(buildDataSourceInfo(file));
        document.setBatch(generateBatchId());
        document.setPosition(position);
        document.setDocumentStatus(DocumentStatus.INDEX_WAITING.getCode());
        document.setEnabled(true);
        document.setArchived(false);
        document.setIsPaused(false);
        document.setUploadTime(LocalDateTime.now());
        document.setUploadBy(userId);
        document.setFileSize(file.getSize());
        document.setDocType(fileExtension);
        document.setDocForm("text_model");
        document.setDocLanguage("zh");
        document.setUpdateTime(LocalDateTime.now());
        document.setCreateBy(userId);

        // 计算文件hash值
        try {
            String fileHash = calculateFileHash(file);
            document.setFileHash(fileHash);

            // 检查是否存在相同hash的文件
            Document existingDoc = baseMapper.selectOne(
                    new QueryWrapper<Document>()
                            .eq("file_hash", fileHash)
                            .eq("dataset_id", datasetId)
                            .ne("name", originalFilename) // 排除同名文件
            );

            if (existingDoc != null) {
                log.warn("发现重复文件: 新文件={}, 已存在文件={}, hash={}",
                        originalFilename, existingDoc.getName(), fileHash);
                // 可以选择抛出异常或者允许重复，这里选择记录警告但允许上传
            }
        } catch (Exception e) {
            log.error("计算文件hash失败: {}", e.getMessage());
            // hash计算失败不影响文件上传，但记录错误
        }

        // 计算文件字符数
        try {
            int wordCount = calculateWordCount(file);
            document.setWordCount(wordCount);
        } catch (Exception e) {
            log.error("计算文件字符数失败: {}", e.getMessage());
            document.setWordCount(0);
        }

        // 设置文档元数据
        Map<String, Object> docMetadata = new HashMap<>();

        docMetadata.put("content_type", file.getContentType());
        document.setDocMetadata(docMetadata);

        return document;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    /**
     * 构建数据源信息
     */
    private String buildDataSourceInfo(MultipartFile file) {
        Map<String, Object> info = new HashMap<>();
        info.put("filename", file.getOriginalFilename());
        info.put("size", file.getSize());
        info.put("content_type", file.getContentType());
        info.put("upload_time", LocalDateTime.now().toString());

        // TODO: 这里应该保存文件到存储系统并返回文件路径
        // String filePath = fileStorageService.saveFile(file);
        // info.put("file_path", filePath);
        return info.toString();
    }

    /**
     * 生成批次ID
     */
    private String generateBatchId() {
        return "batch_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "_" + UUID.randomUUID();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startProcessing(String documentId) {
        log.info("开始处理文档: documentId={}", documentId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        if (!DocumentStatus.INDEX_WAITING.getCode().equals(document.getDocumentStatus())
                && !DocumentStatus.INDEX_ERROR.getCode().equals(document.getDocumentStatus())) {
            throw new BusinessException("文档当前状态不允许开始处理");
        }

        document.setDocumentStatus(DocumentStatus.INDEXING.getCode());
        document.setProcessingStartedAt(LocalDateTime.now());
        document.setError(null);
        document.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(document);
        if (result) {
            log.info("文档开始处理成功: documentId={}", documentId);
            // TODO: 触发异步处理任务
            // processDocumentAsync(documentId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pauseProcessing(String documentId, String userId) {
        log.info("暂停文档处理: documentId={}, userId={}", documentId, userId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        if (!DocumentStatus.INDEXING.getCode().equals(document.getDocumentStatus())) {
            throw new BusinessException("只有处理中的文档才能暂停");
        }

        int result = baseMapper.pauseDocument(documentId, userId);
        if (result > 0) {
            log.info("文档暂停处理成功: documentId={}", documentId);
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resumeProcessing(String documentId) {
        log.info("恢复文档处理: documentId={}", documentId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        if (!document.getIsPaused()) {
            throw new BusinessException("文档未暂停，无需恢复");
        }

        int result = baseMapper.resumeDocument(documentId);
        if (result > 0) {
            log.info("文档恢复处理成功: documentId={}", documentId);
            // 重新触发异步处理任务
            documentProcessingService.processDocumentAsync(documentId, DocumentStatus.PROCESSING);
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reprocessDocument(String documentId) {
        log.info("重新处理文档: documentId={}", documentId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        // 重置文档状态
        document.setDocumentStatus(DocumentStatus.INDEX_WAITING.getCode());
        document.setProcessingStartedAt(null);
        document.setParsingCompletedAt(null);
        document.setCleaningCompletedAt(null);
        document.setSplittingCompletedAt(null);
        document.setCompletedAt(null);
        document.setError(null);
        document.setStoppedAt(null);
        document.setWordCount(null);
        document.setTokens(null);
        document.setIndexingLatency(null);
        document.setIsPaused(false);
        document.setPausedBy(null);
        document.setPausedAt(null);
        document.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(document);
        if (result) {
            log.info("文档重新处理设置成功: documentId={}", documentId);
            // TODO: 删除旧的分段和向量数据
            // deleteDocumentSegments(documentId);
            // deleteDocumentVectors(documentId);

            // 触发异步处理任务
            documentProcessingService.processDocumentAsync(documentId, DocumentStatus.INDEX_WAITING);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEnabled(String documentId, Boolean enabled, String userId) {
        log.info("更新文档启用状态: documentId={}, enabled={}, userId={}", documentId, enabled, userId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        int result = baseMapper.updateEnabled(documentId, enabled, userId);
        if (result > 0) {
            log.info("文档启用状态更新成功: documentId={}, enabled={}", documentId, enabled);
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean archiveDocument(String documentId, String reason, String userId) {
        log.info("归档文档: documentId={}, reason={}, userId={}", documentId, reason, userId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        if (document.getArchived()) {
            throw new BusinessException("文档已归档");
        }

        int result = baseMapper.archiveDocument(documentId, reason, userId);
        if (result > 0) {
            log.info("文档归档成功: documentId={}", documentId);
        }

        return result > 0;
    }

    @Override
    public Object getProcessingProgress(String documentId) {
        log.info("获取文档处理进度: documentId={}", documentId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        Map<String, Object> progress = new HashMap<>();
        progress.put("documentId", documentId);
        progress.put("name", document.getName());
        progress.put("status", document.getDocumentStatus());
        progress.put("enabled", document.getEnabled());
        progress.put("paused", document.getIsPaused());
        progress.put("archived", document.getArchived());

        // 处理时间信息
        progress.put("processingStartedAt", document.getProcessingStartedAt());
        progress.put("parsingCompletedAt", document.getParsingCompletedAt());
        progress.put("cleaningCompletedAt", document.getCleaningCompletedAt());
        progress.put("splittingCompletedAt", document.getSplittingCompletedAt());
        progress.put("completedAt", document.getCompletedAt());

        // 统计信息
        progress.put("wordCount", document.getWordCount());
        progress.put("tokens", document.getTokens());
        progress.put("indexingLatency", document.getIndexingLatency());

        // 错误信息
        if (DocumentStatus.INDEX_ERROR.getCode().equals(document.getDocumentStatus())
                || DocumentStatus.UPLOAD_ERROR.getCode().equals(document.getDocumentStatus())
                || DocumentStatus.SEGMENT_ERROR.getCode().equals(document.getDocumentStatus())) {
            progress.put("error", document.getError());
            progress.put("stoppedAt", document.getStoppedAt());
        }

        // 暂停信息
        if (document.getIsPaused()) {
            progress.put("pausedBy", document.getPausedBy());
            progress.put("pausedAt", document.getPausedAt());
        }

        // 计算处理进度百分比
        String status = document.getDocumentStatus();
        DocumentStatus documentStatus = DocumentStatus.fromCode(status);
        int progressPercent;
        switch (documentStatus) {
            case DocumentStatus.WAITING:
                progressPercent = 0;
                break;
            case DocumentStatus.PARSING:
                progressPercent = 20;
                break;
            case DocumentStatus.CLEANING:
                progressPercent = 40;
                break;
            case DocumentStatus.SEGMENTING:
                progressPercent = 60;
                break;
            case DocumentStatus.INDEXING:
                progressPercent = 80;
                break;
            case DocumentStatus.INDEXED:
                progressPercent = 100;
                break;
            case DocumentStatus.INDEX_ERROR:
            case DocumentStatus.UPLOAD_ERROR:
            case DocumentStatus.SEGMENT_ERROR:
                progressPercent = -1;
                break;
            default:
                progressPercent = 10;
                break;
        }
        progress.put("progressPercent", progressPercent);

        return progress;
    }

    @Override
    public Object getDocumentStats(String documentId) {
        log.info("获取文档统计信息: documentId={}", documentId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        Map<String, Object> stats = new HashMap<>();
        stats.put("documentId", documentId);
        stats.put("name", document.getName());
        stats.put("datasetId", document.getDatasetId());
        stats.put("dataSourceType", document.getDataSourceType());
        stats.put("docType", document.getDocType());
        stats.put("docForm", document.getDocForm());
        stats.put("docLanguage", document.getDocLanguage());

        // 基本统计
        stats.put("wordCount", document.getWordCount());
        stats.put("tokens", document.getTokens());
        stats.put("indexingLatency", document.getIndexingLatency());

        // 状态信息
        stats.put("documentStatus", document.getDocumentStatus());
        stats.put("enabled", document.getEnabled());
        stats.put("archived", document.getArchived());

        // 时间信息
        stats.put("createTime", document.getCreateTime());
        stats.put("updatedAt", document.getUpdateTime());
        stats.put("processingStartedAt", document.getProcessingStartedAt());
        stats.put("completedAt", document.getCompletedAt());

        // 元数据
        stats.put("metadata", document.getDocMetadata());

        // TODO: 添加分段统计信息
        // stats.put("segmentCount", getDocumentSegmentCount(documentId));
        // stats.put("vectorCount", getDocumentVectorCount(documentId));
        return stats;
    }

    @Override
    public List<Document> getWaitingDocuments(Integer limit) {
        return baseMapper.selectWaitingDocuments(limit);
    }

    @Override
    public List<Document> getProcessingDocuments() {
        return baseMapper.selectProcessingDocuments();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProcessingStatus(String documentId, String status) {
        log.info("更新文档处理状态: documentId={}, status={}", documentId, status);

        int result = baseMapper.updateDocumentStatus(documentId, status);
        if (result > 0) {
            log.info("文档处理状态更新成功: documentId={}, status={}", documentId, status);
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProcessingProgress(String documentId, String status, Integer wordCount, Integer tokens) {
        log.info("更新文档处理进度: documentId={}, status={}, wordCount={}, tokens={}",
                documentId, status, wordCount, tokens);

        int result = baseMapper.updateProcessingProgress(documentId, status, wordCount, tokens);
        if (result > 0) {
            log.info("文档处理进度更新成功: documentId={}", documentId);
        }

        return result > 0;
    }

    @Override
    public List<DocumentVO> listByBatch(String batch) {
        return baseMapper.selectByBatch(batch);
    }

    @Override
    public boolean canDelete(String documentId) {
        Document document = getById(documentId);
        if (document == null) {
            return false;
        }

        // 处理中的文档不能删除
        if (DocumentStatus.PROCESSING.getCode().equals(document.getDocumentStatus())) {
            return false;
        }

        // 已归档的文档不能删除
        if (document.getArchived()) {
            return false;
        }

        return true;
    }

    @Override
    public boolean canEdit(String documentId) {
        Document document = getById(documentId);
        if (document == null) {
            return false;
        }

        // 处理中的文档不能编辑
        if (DocumentStatus.PROCESSING.getCode().equals(document.getDocumentStatus())) {
            return false;
        }

        // 已归档的文档不能编辑
        if (document.getArchived()) {
            return false;
        }

        return true;
    }

    /**
     * 解析文档内容（使用Apache Tika）
     */
    private String parseDocumentContent(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            return tika.parseToString(inputStream);
        } catch (IOException | TikaException e) {
            log.error("文档内容解析失败: fileName={}, error={}", file.getOriginalFilename(), e.getMessage());
            throw new BusinessException("文档内容解析失败: " + e.getMessage());
        }
    }

    /**
     * 计算文档字符数
     */
    private int calculateWordCount(String content) {
        if (!StringUtils.hasText(content)) {
            return 0;
        }
        // 简单的字符数统计，可以根据需要优化
        return content.length();
    }

    /**
     * 估算token数量
     */
    private int estimateTokenCount(String content) {
        if (!StringUtils.hasText(content)) {
            return 0;
        }
        // 简单的token估算，中文大约1个字符=1个token，英文大约4个字符=1个token
        // 这里使用简化的估算方法，实际应该使用tokenizer
        return (int) Math.ceil(content.length() * 0.75);
    }

    /**
     * 检查文档数量限制
     */
    private void checkDocumentLimit(String datasetId) {
        Long documentCount = countByDatasetId(datasetId);
        if (documentCount >= ragProperties.getLimits().getMaxDocumentsPerDataset()) {
            throw new BusinessException("知识库文档数量已达上限: " + ragProperties.getLimits().getMaxDocumentsPerDataset());
        }
    }

    /**
     * 检查租户文档限制
     */
    private void checkTenantDocumentLimit(String tenantId) {
        // TODO: 实现租户级别的文档数量限制检查
        // Long tenantDocumentCount = countByTenantId(tenantId);
        // if (tenantDocumentCount >= ragProperties.getLimits().getMaxDocumentsPerTenant()) {
        //     throw new BusinessException("租户文档数量已达上限");
        // }
    }

    /**
     * 计算文件的SHA-256 hash值
     */
    private String calculateFileHash(MultipartFile file) throws IOException, NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");

        try (InputStream inputStream = file.getInputStream()) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }
        }

        byte[] hashBytes = digest.digest();
        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString();
    }

    /**
     * 计算文件的字符数
     */
    private int calculateWordCount(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            String content = parseDocumentContent(inputStream, file.getOriginalFilename());
            if (content == null || content.trim().isEmpty()) {
                return 0;
            }

            // 简单的字符数计算，去除空白字符
            return content.replaceAll("\\s+", "").length();
        } catch (Exception e) {
            log.error("解析文档内容失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 解析文档内容
     */
    private String parseDocumentContent(InputStream inputStream, String filename) throws IOException, TikaException {
        try {
            return tika.parseToString(inputStream);
        } catch (Exception e) {
            log.error("使用Tika解析文档失败: {}", e.getMessage());
            // 如果Tika解析失败，对于文本文件尝试直接读取
            if (filename != null && filename.toLowerCase().endsWith(".txt")) {
                try (Scanner scanner = new Scanner(inputStream, "UTF-8")) {
                    scanner.useDelimiter("\\A");
                    return scanner.hasNext() ? scanner.next() : "";
                }
            }
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteDocuments(List<String> documentIds) {
        if (CollUtil.isEmpty(documentIds)) {
            return;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        log.info("批量删除文档开始 - tenantId={}, userId={}, documentIds={}", tenantId, currentUserId, documentIds);

        for (String documentId : documentIds) {
            try {
                // 检查文档是否存在且属于当前租户
                Document document = getById(documentId);
                if (document == null || !tenantId.equals(document.getTenantId())) {
                    log.warn("文档不存在或无权限删除 - documentId={}", documentId);
                    continue;
                }

                // 检查是否可以删除
                if (!canDelete(documentId)) {
                    log.warn("文档不能删除 - documentId={}", documentId);
                    continue;
                }

                // 删除文档
                deleteDocument(documentId);
                log.info("文档删除成功 - documentId={}", documentId);

            } catch (Exception e) {
                log.error("删除文档失败 - documentId={}, error={}", documentId, e.getMessage(), e);
                // 继续处理其他文档，不中断整个批量操作
            }
        }

        log.info("批量删除文档完成 - documentIds={}", documentIds);
    }

    @Override
    public List<DocumentVO> listByCategoryId(String categoryId) {
        return baseMapper.selectByCategoryId(categoryId);
    }

    @Override
    public Long countByCategoryId(String categoryId) {
        return baseMapper.countByCategoryId(categoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDocumentCategory(String documentId, String categoryId) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 检查文档是否存在且属于当前租户
        Document document = getById(documentId);
        if (document == null || !tenantId.equals(document.getTenantId())) {
            throw new BusinessException("文档不存在或无权限操作");
        }

        baseMapper.updateCategory(documentId, categoryId);
        log.info("更新文档分类成功 - documentId={}, categoryId={}", documentId, categoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateDocumentCategory(List<String> documentIds, String categoryId) {
        if (CollUtil.isEmpty(documentIds)) {
            return;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        log.info("批量更新文档分类开始 - tenantId={}, userId={}, documentIds={}, categoryId={}",
                tenantId, currentUserId, documentIds, categoryId);

        // 检查所有文档是否存在且属于当前租户
        for (String documentId : documentIds) {
            Document document = getById(documentId);
            if (document == null || !tenantId.equals(document.getTenantId())) {
                throw new BusinessException("文档不存在或无权限操作: " + documentId);
            }
        }

        // 批量更新分类
        int updatedCount = baseMapper.batchUpdateCategory(documentIds, categoryId);
        log.info("批量更新文档分类完成 - documentIds={}, categoryId={}, updatedCount={}",
                documentIds, categoryId, updatedCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchStartSegmentation(BatchSegmentationRequestDTO request) {
        log.info("开始批量分段处理: documentIds={}, configSize={}",
                request.getDocumentIds(), request.getDocConfigs().size());

        try {
            String tenantId = SecurityUtils.getCurrentTenantId();
            String userId = SecurityUtils.getCurrentUserId();

            // 验证文档是否存在且属于当前租户
            List<Document> documents = this.listByIds(request.getDocumentIds());
            if (documents.size() != request.getDocumentIds().size()) {
                throw new BusinessException("部分文档不存在或无权限访问");
            }

            // 更新文档状态为等待处理
            for (Document document : documents) {
                document.setDocumentStatus(DocumentStatus.WAITING.getCode());
                document.setProcessingStartedAt(LocalDateTime.now());

                // 更新文档元数据
                FileCleanSegmentConfig fileCleanSegmentConfig = request.getDocConfigs().get(document.getId());
                if (fileCleanSegmentConfig != null) {
                    document.setSegmentConfig(fileCleanSegmentConfig.getSegmentConfig());
                    document.setCleaningConfig(fileCleanSegmentConfig.getCleaningConfig());
                }
            }
            this.updateBatchById(documents);

            // 发送批量分段处理消息到RabbitMQ ，TODO 此处要改成每个文件往队列发送
            if (rabbitMQProducer != null) {
                Map<String, Object> segmentationData = new HashMap<>();
                segmentationData.put("documentIds", request.getDocumentIds());
                segmentationData.put("docConfigs", request.getDocConfigs());
                segmentationData.put("segmentationConfig", request.getSegmentationConfig());
                segmentationData.put("tenantId", tenantId);
                segmentationData.put("userId", userId);

                rabbitMQProducer.sendDocumentSegmentationMessage(segmentationData, tenantId, userId);
                log.info("批量分段处理消息发送成功: documentIds={}", request.getDocumentIds());
            } else {
                log.warn("RabbitMQ生产者未配置，无法发送批量分段处理消息");
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("批量分段处理启动失败: {}", e.getMessage(), e);
            throw new BusinessException("批量分段处理启动失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveDocumentConfigs(FileCleanSegmentConfigParamsDTO request) {
        try {
            // 获取当前用户信息
            String currentUserId = SecurityUtils.getCurrentUserId();
            String tenantId = SecurityUtils.getCurrentTenantId();

            // 验证权限
//            if (!SecurityUtils.hasPermission("rag:document:update")) {
//                throw new BusinessException("没有文档更新权限");
//            }

            // 1. 保存全局配置（如果需要的话，可以保存到系统配置表）
            // 这里暂时跳过全局配置的持久化，因为全局配置通常在前端管理

            // 2. 批量更新有自定义配置的文档
            if (request.getDocumentIds() != null && !request.getDocumentIds().isEmpty()) {
                List<Document> documentsToUpdate = new ArrayList<>();
                for (int i = 0; i < request.getDocumentIds().size(); i++) {
                    String documentId = request.getDocumentIds().get(i);
                       // 检查权限
//                    if (!document.getCreateBy().equals(currentUserId)
//                        && !SecurityUtils.hasPermission("rag:document:manage")) {
//                        log.warn("没有权限更新文档配置: documentId={}", documentId);
//                        continue;
//                    }

                    Document document = getById(documentId);
                    if (document == null) {
                        log.warn("文档不存在，跳过配置保存: documentId={}", documentId);
                        continue;
                    }

                    if (document != null) {
                        // 对指定文件特殊配置进行保存
                        if (request.getDocConfigs() !=null && request.getDocConfigs().containsKey(documentId)) {
                            document.setSegmentConfig(request.getDocConfigs().get(documentId).getSegmentConfig());
                            document.setCleaningConfig(request.getDocConfigs().get(documentId).getCleaningConfig());
                            document.setUpdateTime(LocalDateTime.now());
                            documentsToUpdate.add(document);
                        } else {
                            // 将默认SegmentConfig和CleaningConfig配置信息保存到文档对象中
                            document.setSegmentConfig(request.getSegmentConfig());
                            document.setCleaningConfig(request.getCleaningConfig());
                            document.setUpdateTime(LocalDateTime.now());
                            documentsToUpdate.add(document);

                        }
                    }
                }
                // 批量更新文档
                if (!documentsToUpdate.isEmpty()) {
                    boolean updateResult = updateBatchById(documentsToUpdate);
                    if (updateResult) {
                        log.info("批量保存文档配置成功: 更新文档数量={}", documentsToUpdate.size());
                    } else {
                        log.error("批量保存文档配置失败");
                        return false;
                    }
                }
            }

            return true;

        } catch (Exception e) {
            log.error("批量保存文档配置失败: {}", e.getMessage(), e);
            throw new BusinessException("批量保存文档配置失败: " + e.getMessage());
        }
    }
}
