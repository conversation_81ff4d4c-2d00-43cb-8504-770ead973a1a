# DocumentMapper.java 修改说明

## 修改目的
根据KnowledgeSegmentConfigMapper.java中对segmentConfig和cleaningConfig字段的查询规范，修改DocumentMapper.java中的相关查询接口，确保这两个JSON字段能够正确地进行序列化和反序列化。

## 参考规范
参照KnowledgeSegmentConfigMapper.java中的实现：
```java
@Results(id = "KnowledgeSegmentConfigResultMap", value = {
    @Result(column = "segment_config", property = "segmentConfig",
            typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = JdbcType.OTHER),
    @Result(column = "cleaning_config", property = "cleaningConfig",
            typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = JdbcType.OTHER)
})
```

## 主要修改内容

### 1. 添加导入
```java
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
```

### 2. 解决@Results注解冲突问题
由于`@Results`注解不支持在同一个接口中重复使用，我们采用在每个查询方法上直接定义`@Results`的方式：

```java
@Results({
    @Result(column = "segment_config", property = "segmentConfig",
            typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = JdbcType.OTHER),
    @Result(column = "cleaning_config", property = "cleaningConfig",
            typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = JdbcType.OTHER)
})
```

### 3. 修改的查询方法

所有需要处理segmentConfig和cleaningConfig字段的查询方法都添加了相同的@Results注解：

1. **selectPageByDataset** - 根据知识库ID分页查询文档列表
   ```java
   @Results({
       @Result(column = "segment_config", property = "segmentConfig",
               typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = JdbcType.OTHER),
       @Result(column = "cleaning_config", property = "cleaningConfig",
               typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = JdbcType.OTHER)
   })
   IPage<DocumentVO> selectPageByDataset(...);
   ```

2. **selectByDocumentId** - 根据文档ID查询文档信息
3. **selectByDatasetId** - 根据知识库ID查询文档列表
4. **selectByBatch** - 根据批次号查询文档列表
5. **selectWaitingDocuments** - 查询等待处理的文档列表
6. **selectProcessingDocuments** - 查询处理中的文档列表
7. **selectByCategoryId** - 根据分类ID查询文档列表

每个方法都使用相同的@Results注解来处理JSON字段。

## TypeHandler说明

### SegmentConfigTypeHandler
- 处理segment_config字段的JSON序列化/反序列化
- 将数据库中的JSONB数据转换为SegmentConfig对象
- 支持PostgreSQL的JSONB类型

### CleaningConfigTypeHandler
- 处理cleaning_config字段的JSON序列化/反序列化
- 将数据库中的JSONB数据转换为CleaningConfig对象
- 支持PostgreSQL的JSONB类型

## 数据结构

### SegmentConfig
```java
public class SegmentConfig {
    private String type; // directory | natural | delimiter | constantLength | none
    private DirectoryConfig directory;
    private NaturalConfig natural;
    private DelimiterConfig delimiter;
    private ConstantLengthConfig constantLength;
}
```

### CleaningConfig
```java
public class CleaningConfig {
    private Boolean removeEmptyLines;
    private Boolean removeExtraSpaces;
    private Boolean removeSpecialChars;
    private Boolean normalizeText;
    private Boolean deleteSymbol;
    private Boolean deleteInlineMedia;
    private String filterKeywords;
}
```

## 修改效果

1. **正确的JSON处理**：segmentConfig和cleaningConfig字段现在能够正确地在数据库JSONB类型和Java对象之间转换
2. **统一的处理规范**：与KnowledgeSegmentConfigMapper保持一致的处理方式
3. **完整的字段映射**：所有查询方法都能正确返回包含配置信息的完整对象
4. **类型安全**：通过TypeHandler确保数据类型的正确性和一致性

## 注意事项

1. **数据库字段类型**：确保数据库中segment_config和cleaning_config字段为JSONB类型
2. **TypeHandler注册**：确保SegmentConfigTypeHandler和CleaningConfigTypeHandler已正确注册
3. **JSON格式**：存储在数据库中的JSON数据必须符合对应Java类的结构
4. **性能考虑**：JSONB字段的查询和更新可能比普通字段稍慢，但提供了更好的灵活性

## 测试建议

1. **单元测试**：为每个修改的查询方法编写单元测试
2. **集成测试**：测试完整的数据流，从前端配置到数据库存储
3. **性能测试**：验证JSON字段处理的性能影响
4. **数据一致性测试**：确保配置数据的正确性和完整性
