package com.xhcai.modules.rag.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.rag.dto.BatchSegmentationRequestDTO;
import com.xhcai.modules.rag.dto.FileCleanSegmentConfigParamsDTO;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.vo.DocumentVO;

/**
 * 文档服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IDocumentService extends IService<Document> {

    /**
     * 分页查询文档列表
     *
     * @param current 当前页
     * @param size 每页大小
     * @param datasetId 知识库ID
     * @param name 文档名称（模糊查询）
     * @param documentStatus 索引状态
     * @param enabled 是否启用
     * @param batch 用户上传的批次号
     * @return 文档分页列表
     */
    IPage<DocumentVO> pageByDataset(Long current, Long size, String datasetId, String name,
            String documentStatus, Boolean enabled, String batch);

    /**
     * 根据知识库ID查询文档列表
     *
     * @param datasetId 知识库ID
     * @return 文档列表
     */
    List<DocumentVO> listByDatasetId(String datasetId);

    /**
     * 批量上传文档
     *
     * @param files 文件列表
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @param batchId 用户上传文件批次号
     * @return 上传的文档列表
     */
    List<DocumentVO> batchUploadDocuments(List<MultipartFile> files, String datasetId, String userId, String batchId);

//    /**
//     * 创建文本文档
//     *
//     * @param name 文档名称
//     * @param content 文档内容
//     * @param datasetId 知识库ID
//     * @param userId 用户ID
//     * @return 创建的文档
//     */
//    Document createTextDocument(String name, String content, String datasetId, String userId);
    /**
     * 更新文档
     *
     * @param document 文档信息
     * @return 更新的文档
     */
    Document updateDocument(Document document);

    /**
     * 删除文档
     *
     * @param id 文档ID
     * @return 是否删除成功
     */
    boolean deleteDocument(String id);

    /**
     * 根据ID查询文档详情
     *
     * @param id 文档ID
     * @return 文档详情
     */
    Document getDocumentById(String id);

    /**
     * 统计知识库的文档数量
     *
     * @param datasetId 知识库ID
     * @return 文档数量
     */
    Long countByDatasetId(String datasetId);

    /**
     * 根据知识库ID和状态统计文档数量
     *
     * @param datasetId 知识库ID
     * @param documentStatus 索引状态
     * @return 文档数量
     */
    Long countByDatasetIdAndStatus(String datasetId, String documentStatus);

    /**
     * 开始处理文档
     *
     * @param documentId 文档ID
     * @return 是否开始成功
     */
    boolean startProcessing(String documentId);

    /**
     * 暂停文档处理
     *
     * @param documentId 文档ID
     * @param userId 用户ID
     * @return 是否暂停成功
     */
    boolean pauseProcessing(String documentId, String userId);

    /**
     * 恢复文档处理
     *
     * @param documentId 文档ID
     * @return 是否恢复成功
     */
    boolean resumeProcessing(String documentId);

    /**
     * 重新处理文档
     *
     * @param documentId 文档ID
     * @return 是否重新处理成功
     */
    boolean reprocessDocument(String documentId);

    /**
     * 启用/禁用文档
     *
     * @param documentId 文档ID
     * @param enabled 是否启用
     * @param userId 用户ID
     * @return 是否操作成功
     */
    boolean updateEnabled(String documentId, Boolean enabled, String userId);

    /**
     * 归档文档
     *
     * @param documentId 文档ID
     * @param reason 归档原因
     * @param userId 用户ID
     * @return 是否归档成功
     */
    boolean archiveDocument(String documentId, String reason, String userId);

    /**
     * 获取文档处理进度
     *
     * @param documentId 文档ID
     * @return 处理进度信息
     */
    Object getProcessingProgress(String documentId);

    /**
     * 获取文档统计信息
     *
     * @param documentId 文档ID
     * @return 统计信息
     */
    Object getDocumentStats(String documentId);

    /**
     * 查询等待处理的文档列表
     *
     * @param limit 限制数量
     * @return 文档列表
     */
    List<Document> getWaitingDocuments(Integer limit);

    /**
     * 查询处理中的文档列表
     *
     * @return 文档列表
     */
    List<Document> getProcessingDocuments();

    /**
     * 更新文档处理状态
     *
     * @param documentId 文档ID
     * @param status 状态
     * @return 是否更新成功
     */
    boolean updateProcessingStatus(String documentId, String status);

    /**
     * 更新文档处理进度
     *
     * @param documentId 文档ID
     * @param status 状态
     * @param wordCount 字符数
     * @param tokens token数
     * @return 是否更新成功
     */
    boolean updateProcessingProgress(String documentId, String status, Integer wordCount, Integer tokens);

    /**
     * 根据批次号查询文档列表
     *
     * @param batch 批次号
     * @return 文档列表
     */
    List<DocumentVO> listByBatch(String batch);

    /**
     * 检查文档是否可以删除
     *
     * @param documentId 文档ID
     * @return 是否可以删除
     */
    boolean canDelete(String documentId);

    /**
     * 检查文档是否可以编辑
     *
     * @param documentId 文档ID
     * @return 是否可以编辑
     */
    boolean canEdit(String documentId);

    /**
     * 批量删除文档
     *
     * @param documentIds 文档ID列表
     */
    void batchDeleteDocuments(List<String> documentIds);

    /**
     * 根据分类ID查询文档列表
     *
     * @param categoryId 分类ID
     * @return 文档列表
     */
    List<DocumentVO> listByCategoryId(String categoryId);

    /**
     * 根据分类ID统计文档数量
     *
     * @param categoryId 分类ID
     * @return 文档数量
     */
    Long countByCategoryId(String categoryId);

    /**
     * 更新文档分类
     *
     * @param documentId 文档ID
     * @param categoryId 分类ID
     */
    void updateDocumentCategory(String documentId, String categoryId);

    /**
     * 批量更新文档分类
     *
     * @param documentIds 文档ID列表
     * @param categoryId 分类ID
     */
    void batchUpdateDocumentCategory(List<String> documentIds, String categoryId);

    /**
     * 批量开始分段处理
     *
     * @param request 批量分段处理请求
     * @return 是否成功
     */
    boolean batchStartSegmentation(BatchSegmentationRequestDTO request);

    /**
     * 批量保存文档配置
     *
     * @param request 文档配置参数
     * @return 是否成功
     */
    boolean batchSaveDocumentConfigs(FileCleanSegmentConfigParamsDTO request);
}
