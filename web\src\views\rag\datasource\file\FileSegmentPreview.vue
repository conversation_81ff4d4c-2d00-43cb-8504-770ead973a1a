<template>
  <div class="relative z-1000">
    <!-- 侧边面板 -->
    <div
      class="preview-panel"
      :class="{ 'panel-open': isOpen }"
    >
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="file-info">
          <div class="file-icon">
            {{configStore.getFileTypeIcon(currentFile)}}
          </div>
          <div class="file-details">
            <h4 class="file-name">{{ currentFile?.name }}</h4>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(currentFile?.fileSize || 0) }}</span>
              <span class="segment-count">{{ currentFile?.segmentCount || 0 }} 个分段</span>
            </div>
          </div>
        </div>
        <button class="close-btn" @click="closePanel">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 分段配置区 -->
      <div class="flex-1 flex flex-col max-h-[calc(100vh-90px)] bg-white border border-gray-200 border-solid">
        <div class="config-header">
          <h5>分段配置</h5>
          <div class="config-actions">
            <button
              class="btn-reset"
              @click="resetToDefault"
              :disabled="!hasCustomConfig"
            >
              <i class="fas fa-undo"></i>
              使用默认
            </button>
            <button
              class="btn-save"
              @click="saveConfig"
              :disabled="!configChanged"
            >
              <i class="fas fa-save"></i>
              保存配置
            </button>
          </div>
        </div>

        <!-- 使用SegmentConfigPanel组件 -->
        <div class="config-panel-wrapper">
          <SegmentConfigPanel/>
        </div>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div 
      v-if="isOpen" 
      class="panel-overlay"
      @click="closePanel"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineEmits, defineProps, onMounted, onUnmounted } from 'vue'
import type { UploadedFile, FileSegment } from '@/types/rag'
import SegmentConfigPanel from './SegmentConfigPanel.vue'
import { useRagConfigStore } from '@/stores/ragConfigStore'
import { useGlobalRagEvents } from '@/composables/useRagConfigEvents'

// 定义事件
const emit = defineEmits(['close', 'config-updated'])

// 定义 props
const props = defineProps<{
  isOpen: boolean
  currentFile: UploadedFile | null
}>()

// 统一状态管理
const configStore = useRagConfigStore()
const eventBus = useGlobalRagEvents()

const segments = ref<FileSegment[]>([])

// 计算属性
const hasCustomConfig = computed(() => {
  return props.currentFile ? configStore.fileConfigs.has(props.currentFile.id) : false
})

const configChanged = computed(() => {
  if (!props.currentFile) return false
  const defaultConfig = configStore.DEFAULT_SEGMENT_CONFIG
  return JSON.stringify(localConfig.value) !== JSON.stringify(defaultConfig)
})

const estimatedTime = computed(() => {
  return Math.ceil(segments.value.length * 0.1)
})

// 方法
const closePanel = () => {
  emit('close')
}


const resetToDefault = () => {
  if (props.currentFile) {
    eventBus.handleConfigReset('FileSegmentPreview', props.currentFile.id)
    generatePreview()
  }
}

const saveConfig = () => {
  if (props.currentFile) {
    emit('config-updated', props.currentFile.id, { ...localConfig.value })
  }
}

const generatePreview = () => {
  if (!props.currentFile) {
    segments.value = []
    return
  }

  // 模拟生成分段预览
  const mockSegments = Array.from({ length: Math.floor(Math.random() * 8) + 3 }, (_, i) => ({
    id: `segment-${i}`,
    content: `这是 ${props.currentFile?.name} 的第 ${i + 1} 个分段的示例内容。根据当前配置，每个分段大约包含 ${localConfig.value.max_len} 个字符。这里是一些示例文本内容...`,
    length: Math.floor(Math.random() * 200) + localConfig.value.max_len - 100,
    index: i
  }))

  segments.value = mockSegments
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 标记是否正在内部更新，避免递归
const isInternalUpdate = ref(false)

// 监听文件变化
watch(() => props.currentFile, (newFile) => {
  if (newFile) {
    // 设置当前活动文件
    configStore.setActiveFile(newFile.id)
    generatePreview()
  }
}, { immediate: true })

// 监听配置变化
watch(configStore.currentConfig, () => {
  if (!isInternalUpdate.value) {
    generatePreview()
  }
}, { deep: true })
</script>

<style scoped>
.preview-panel {
  position: fixed;
  top: 0;
  left: -90vw;
  width: 90vw;
  height: 100vh;
  background: white;
  border-right: 1px solid #e5e7eb;
  box-shadow: 4px 0 12px rgba(0, 0, 0, 0.1);
  transition: left 0.3s ease;
  display: flex;
  flex-direction: column;
  z-index: 1001;
}

.preview-panel.panel-open {
  left: 0;
}

.panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 8px;
  font-size: 18px;
}

.file-details {
  flex: 1;
}

.file-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.file-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 主要内容区域 - 左右布局 */
.panel-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧配置区域 */
.config-area {
  width: 560px;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
}

.config-header h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.config-panel-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px;
  min-height: 0;
}

/* 右侧预览区域 */
.preview-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.preview-header h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.config-actions {
  display: flex;
  gap: 8px;
}

.btn-reset, .btn-save {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-reset {
  background: #f3f4f6;
  color: #6b7280;
}

.btn-save {
  background: #3b82f6;
  color: white;
}

.btn-reset:disabled, .btn-save:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input, .form-number {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
}

.input-with-slider {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-slider {
  flex: 1;
  height: 4px;
}

.form-number {
  width: 60px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  cursor: pointer;
}

.segments-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.segment-item {
  margin-bottom: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 11px;
}

.segment-index {
  font-weight: 500;
  color: #3b82f6;
}

.segment-length {
  color: #6b7280;
}

.segment-content {
  font-size: 12px;
  color: #374151;
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.no-segments {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6b7280;
  text-align: center;
}

.no-segments i {
  font-size: 32px;
  margin-bottom: 12px;
  color: #d1d5db;
}

.preview-stats {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #6b7280;
}
</style>
