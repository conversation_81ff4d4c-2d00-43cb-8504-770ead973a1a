package com.xhcai.modules.rag.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.dto.BatchSegmentationRequestDTO;
import com.xhcai.modules.rag.dto.DocumentQueryDTO;
import com.xhcai.modules.rag.dto.DocumentPermissionDTO;
import com.xhcai.modules.rag.dto.FileCleanSegmentConfigParamsDTO;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.service.IDocumentService;
import com.xhcai.modules.rag.service.IDocumentPermissionService;
import com.xhcai.modules.rag.vo.DocumentVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * 文档管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "文档管理", description = "文档的上传、查询、更新、删除等操作")
@RestController
@RequestMapping("/api/rag/documents")
@Validated
public class DocumentController {

    private static final Logger log = LoggerFactory.getLogger(DocumentController.class);

    @Autowired
    private IDocumentService documentService;

    @Autowired
    private IDocumentPermissionService documentPermissionService;

    @Operation(summary = "分页查询文档列表", description = "根据条件分页查询文档列表")
    @GetMapping("/page")
    @RequiresPermissions("rag:document:list")
    public Result<PageResult<DocumentVO>> page(@Parameter(description = "查询条件") DocumentQueryDTO queryDTO) {
        log.info("分页查询文档列表: {}", queryDTO);

        IPage<DocumentVO> page = documentService.pageByDataset(
                queryDTO.getCurrent(),
                queryDTO.getSize(),
                queryDTO.getDatasetId(),
                queryDTO.getName(),
                queryDTO.getDocumentStatus(),
                queryDTO.getEnabled(),
                queryDTO.getBatch()
        );

        PageResult<DocumentVO> pageResult = new PageResult<>(
                page.getRecords(),
                page.getTotal(),
                page.getCurrent(),
                page.getSize()
        );

        return Result.success(pageResult);
    }

    @Operation(summary = "查询文档列表", description = "查询指定知识库的所有文档")
    @GetMapping("/list")
    @RequiresPermissions("rag:document:list")
    public Result<List<DocumentVO>> list(@Parameter(description = "知识库ID") @RequestParam String datasetId) {
        log.info("查询文档列表: datasetId={}", datasetId);

        List<DocumentVO> documents = documentService.listByDatasetId(datasetId);

        return Result.success(documents);
    }

    @Operation(summary = "查询文档详情", description = "根据ID查询文档详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("rag:document:detail")
    public Result<DocumentVO> getById(@Parameter(description = "文档ID") @PathVariable String id) {
        log.info("查询文档详情: id={}", id);

        Document document = documentService.getDocumentById(id);
        if (document == null) {
            return Result.fail("文档不存在");
        }

        DocumentVO vo = convertToVO(document);
        return Result.success(vo);
    }

    @Operation(summary = "上传文档", description = "上传单个文档到知识库")
    @PostMapping("/upload")
    @RequiresPermissions("rag:document:upload")
    public Result<List<DocumentVO>> upload(
            @Parameter(description = "文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "上传批次ID") @RequestParam(value = "batchId", required = false) String batchId,
            @Parameter(description = "知识库ID") @RequestParam String datasetId) {
        log.info("上传文档: fileName={}, datasetId={}", file.getOriginalFilename(), datasetId);

        List<DocumentVO> documents = documentService.batchUploadDocuments(List.of(file), datasetId, null, batchId);

        return Result.success(documents);
    }

    @Operation(summary = "批量上传文档", description = "批量上传文档到知识库")
    @PostMapping("/batch-upload")
    @RequiresPermissions("rag:document:upload")
    public Result<List<DocumentVO>> batchUpload(
            @Parameter(description = "文件列表") @RequestParam("files") List<MultipartFile> files,
            @Parameter(description = "上传批次ID") @RequestParam(value = "batchId", required = false) String batchId,
            @Parameter(description = "知识库ID") @RequestParam(value = "datasetId", required = false) String datasetId) {
        log.info("批量上传文档: fileCount={}, datasetId={}", files.size(), datasetId);

        // 如果没有提供 datasetId，使用默认值
        if (datasetId == null || datasetId.trim().isEmpty()) {
            log.info("使用默认数据集ID: {}", datasetId);
            return Result.fail("请提供知识库ID");
        }

        List<DocumentVO> documents = documentService.batchUploadDocuments(files, datasetId, null, batchId);

        return Result.success(documents);
    }

    @Operation(summary = "删除文档", description = "删除指定的文档")
    @DeleteMapping("/{id}")
    @RequiresPermissions("rag:document:delete")
    public Result<Void> delete(@Parameter(description = "文档ID") @PathVariable String id) {
        log.info("删除文档: id={}", id);

        boolean success = documentService.deleteDocument(id);
        if (success) {
            return Result.success();
        } else {
            return Result.fail("删除失败");
        }
    }

    @Operation(summary = "开始处理文档", description = "开始处理指定文档")
    @PostMapping("/{id}/start-processing")
    @RequiresPermissions("rag:document:update")
    public Result<Void> startProcessing(@Parameter(description = "文档ID") @PathVariable String id) {
        log.info("开始处理文档: id={}", id);

        boolean success = documentService.startProcessing(id);
        if (success) {
            return Result.success();
        } else {
            return Result.fail("开始处理失败");
        }
    }

    @Operation(summary = "暂停处理文档", description = "暂停处理指定文档")
    @PostMapping("/{id}/pause-processing")
    @RequiresPermissions("rag:document:update")
    public Result<Void> pauseProcessing(@Parameter(description = "文档ID") @PathVariable String id) {
        log.info("暂停处理文档: id={}", id);

        // TODO: 从当前用户上下文获取用户ID
        String userId = "current_user_id";

        boolean success = documentService.pauseProcessing(id, userId);
        if (success) {
            return Result.success();
        } else {
            return Result.fail("暂停处理失败");
        }
    }

    @Operation(summary = "恢复处理文档", description = "恢复处理指定文档")
    @PostMapping("/{id}/resume-processing")
    @RequiresPermissions("rag:document:update")
    public Result<Void> resumeProcessing(@Parameter(description = "文档ID") @PathVariable String id) {
        log.info("恢复处理文档: id={}", id);

        boolean success = documentService.resumeProcessing(id);
        if (success) {
            return Result.success();
        } else {
            return Result.fail("恢复处理失败");
        }
    }

    @Operation(summary = "重新处理文档", description = "重新处理指定文档")
    @PostMapping("/{id}/reprocess")
    @RequiresPermissions("rag:document:update")
    public Result<Void> reprocess(@Parameter(description = "文档ID") @PathVariable String id) {
        log.info("重新处理文档: id={}", id);

        boolean success = documentService.reprocessDocument(id);
        if (success) {
            return Result.success();
        } else {
            return Result.fail("重新处理失败");
        }
    }

    @Operation(summary = "批量开始分段处理", description = "批量开始文档分段处理")
    @PostMapping("/batch/start-segmentation")
    @RequiresPermissions("rag:document:update")
    public Result<Void> batchStartSegmentation(
            @Parameter(description = "批量分段处理请求") @RequestBody BatchSegmentationRequestDTO request) {
        log.info("批量开始分段处理: documentIds={}, configSize={}",
                request.getDocumentIds(), request.getDocConfigs().size());

        try {
            boolean success = documentService.batchStartSegmentation(request);
            if (success) {
                return Result.success();
            } else {
                return Result.fail("批量分段处理启动失败");
            }
        } catch (Exception e) {
            log.error("批量分段处理启动异常: {}", e.getMessage(), e);
            return Result.fail("批量分段处理启动异常: " + e.getMessage());
        }
    }

    @Operation(summary = "批量保存文档配置", description = "批量保存文档的分段配置和清洗配置")
    @PostMapping("/batch/save-configs")
    @RequiresPermissions("rag:document:update")
    public Result<Void> batchSaveDocumentConfigs(
            @Parameter(description = "文档配置参数") @RequestBody FileCleanSegmentConfigParamsDTO request) {
        log.info("批量保存文档配置: globalConfig={}, customConfigCount={}",
                request.getSegmentConfig() != null, request.getDocConfigs().size());

        try {
            boolean success = documentService.batchSaveDocumentConfigs(request);
            if (success) {
                return Result.success();
            } else {
                return Result.fail("批量保存文档配置失败");
            }
        } catch (Exception e) {
            log.error("批量保存文档配置异常", e);
            return Result.fail("批量保存文档配置异常: " + e.getMessage());
        }
    }

    @Operation(summary = "启用/禁用文档", description = "启用或禁用指定文档")
    @PutMapping("/{id}/enabled")
    @RequiresPermissions("rag:document:update")
    public Result<Void> updateEnabled(
            @Parameter(description = "文档ID") @PathVariable String id,
            @Parameter(description = "是否启用") @RequestParam Boolean enabled) {
        log.info("更新文档启用状态: id={}, enabled={}", id, enabled);

        // TODO: 从当前用户上下文获取用户ID
        String userId = "current_user_id";

        boolean success = documentService.updateEnabled(id, enabled, userId);
        if (success) {
            return Result.success();
        } else {
            return Result.fail("更新失败");
        }
    }

    @Operation(summary = "归档文档", description = "归档指定文档")
    @PostMapping("/{id}/archive")
    @RequiresPermissions("rag:document:update")
    public Result<Void> archive(
            @Parameter(description = "文档ID") @PathVariable String id,
            @Parameter(description = "归档原因") @RequestParam String reason) {
        log.info("归档文档: id={}, reason={}", id, reason);

        // TODO: 从当前用户上下文获取用户ID
        String userId = "current_user_id";

        boolean success = documentService.archiveDocument(id, reason, userId);
        if (success) {
            return Result.success();
        } else {
            return Result.fail("归档失败");
        }
    }

    @Operation(summary = "获取处理进度", description = "获取文档的处理进度")
    @GetMapping("/{id}/progress")
    @RequiresPermissions("rag:document:detail")
    public Result<Object> getProgress(@Parameter(description = "文档ID") @PathVariable String id) {
        log.info("获取文档处理进度: id={}", id);

        Object progress = documentService.getProcessingProgress(id);
        return Result.success(progress);
    }

    @Operation(summary = "获取统计信息", description = "获取文档的统计信息")
    @GetMapping("/{id}/stats")
    @RequiresPermissions("rag:document:detail")
    public Result<Object> getStats(@Parameter(description = "文档ID") @PathVariable String id) {
        log.info("获取文档统计信息: id={}", id);

        Object stats = documentService.getDocumentStats(id);
        return Result.success(stats);
    }

    @Operation(summary = "统计文档数量", description = "统计指定知识库的文档数量")
    @GetMapping("/count")
    @RequiresPermissions("rag:document:list")
    public Result<Long> count(@Parameter(description = "知识库ID") @RequestParam String datasetId) {
        log.info("统计文档数量: datasetId={}", datasetId);

        Long count = documentService.countByDatasetId(datasetId);
        return Result.success(count);
    }

    @Operation(summary = "根据批次查询文档", description = "根据批次号查询文档列表")
    @GetMapping("/batch/{batch}")
    @RequiresPermissions("rag:document:list")
    public Result<List<DocumentVO>> listByBatch(@Parameter(description = "批次号") @PathVariable String batch) {
        log.info("根据批次查询文档: batch={}", batch);

        List<DocumentVO> documents = documentService.listByBatch(batch);

        return Result.success(documents);
    }

    @Operation(summary = "下载文档", description = "下载指定的文档文件")
    @GetMapping("/{id}/download")
    @RequiresPermissions("rag:document:download")
    public Result<String> downloadDocument(@Parameter(description = "文档ID") @PathVariable String id) {
        log.info("下载文档: id={}", id);

        // TODO: 实现文档下载逻辑
        // 这里应该返回文件下载URL或直接返回文件流
        return Result.success("下载链接或文件流");
    }

    @Operation(summary = "批量下载文档", description = "批量下载多个文档文件")
    @PostMapping("/batch/download")
    @RequiresPermissions("rag:document:download")
    public Result<String> batchDownloadDocuments(
            @Parameter(description = "文档ID列表") @RequestBody List<String> documentIds) {
        log.info("批量下载文档: documentIds={}", documentIds);

        // TODO: 实现批量文档下载逻辑
        // 这里应该返回压缩包下载URL或直接返回压缩文件流
        return Result.success("批量下载链接或压缩文件流");
    }

    @Operation(summary = "批量删除文档", description = "批量删除多个文档")
    @PostMapping("/batch/delete")
    @RequiresPermissions("rag:document:delete")
    public Result<Void> batchDeleteDocuments(
            @Parameter(description = "文档ID列表") @RequestBody List<String> documentIds) {
        log.info("批量删除文档: documentIds={}", documentIds);

        documentService.batchDeleteDocuments(documentIds);
        return Result.success();
    }

    @Operation(summary = "设置文档权限", description = "设置文档的访问权限")
    @PostMapping("/permissions")
    @RequiresPermissions("rag:document:permission")
    public Result<Void> setDocumentPermission(
            @Parameter(description = "权限设置信息") @RequestBody DocumentPermissionDTO permissionDTO) {
        log.info("设置文档权限: {}", permissionDTO);

        documentPermissionService.setDocumentPermission(permissionDTO);
        return Result.success();
    }

    @Operation(summary = "获取文档权限", description = "获取指定文档的权限设置")
    @GetMapping("/{id}/permissions")
    @RequiresPermissions("rag:document:permission")
    public Result<List<com.xhcai.modules.rag.entity.DocumentPermission>> getDocumentPermissions(
            @Parameter(description = "文档ID") @PathVariable String id) {
        log.info("获取文档权限: id={}", id);

        List<com.xhcai.modules.rag.entity.DocumentPermission> permissions
                = documentPermissionService.getDocumentPermissions(id);
        return Result.success(permissions);
    }

    @Operation(summary = "更新文档分类", description = "更新指定文档的分类")
    @PutMapping("/{id}/category")
    @RequiresPermissions("rag:document:update")
    public Result<Void> updateDocumentCategory(
            @Parameter(description = "文档ID") @PathVariable String id,
            @Parameter(description = "分类ID") @RequestParam(required = false) String categoryId) {
        log.info("更新文档分类: id={}, categoryId={}", id, categoryId);

        documentService.updateDocumentCategory(id, categoryId);
        return Result.success();
    }

    @Operation(summary = "批量更新文档分类", description = "批量更新多个文档的分类")
    @PostMapping("/batch/category")
    @RequiresPermissions("rag:document:update")
    public Result<Void> batchUpdateDocumentCategory(
            @Parameter(description = "文档ID列表") @RequestParam List<String> documentIds,
            @Parameter(description = "分类ID") @RequestParam(required = false) String categoryId) {
        log.info("批量更新文档分类: documentIds={}, categoryId={}", documentIds, categoryId);

        documentService.batchUpdateDocumentCategory(documentIds, categoryId);
        return Result.success();
    }

    @Operation(summary = "根据分类查询文档", description = "根据分类ID查询文档列表")
    @GetMapping("/category/{categoryId}")
    @RequiresPermissions("rag:document:list")
    public Result<List<DocumentVO>> getDocumentsByCategory(
            @Parameter(description = "分类ID") @PathVariable String categoryId) {
        log.info("根据分类查询文档: categoryId={}", categoryId);

        List<DocumentVO> documents = documentService.listByCategoryId(categoryId);

        return Result.success(documents);
    }

    /**
     * 转换为VO对象
     *
     * @param document 实体对象
     * @return VO对象
     */
    private DocumentVO convertToVO(Document document) {
        DocumentVO vo = new DocumentVO();
        BeanUtils.copyProperties(document, vo);

        // 设置数据源类型描述
        if (document.getDataSourceType() != null) {
            vo.setDataSourceTypeDesc(document.getDataSourceType());
        }

        // 设置索引状态描述
        if (document.getDocumentStatus() != null) {
            vo.setDocumentStatusDesc(document.getDocumentStatus());
        }

        // TODO: 设置知识库名称
        // TODO: 设置创建人和暂停人姓名
        // TODO: 设置统计信息（分段数量、处理进度等）
        return vo;
    }
}
